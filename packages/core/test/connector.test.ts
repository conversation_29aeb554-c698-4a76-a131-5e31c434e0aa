import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest'
import { ws, type WebSocketHandlerConnection } from 'msw'
import { setupServer } from 'msw/node'
import ExpiryMap from 'expiry-map'
import EventEmitter from 'eventemitter3'
import { WebshellConnector } from '../src/connector'
import {
  jsonrpcRequest,
  jsonrpcNotification,
  jsonrpcSuccessRespone,
  jsonrpcErrorResponse,
  isJsonrpcRequest,
  isJsonrpcNotification,
  parseJsonrpcMessage,
  type JsonrpcRequest,
  type JsonrpcNotification,
} from '../src/utils/json-rpc'

// 创建 WebSocket link
const testUrl = 'ws://localhost:8080/test'
const wsLink = ws.link(testUrl)

const testRpcSuccess = {
  request: jsonrpcRequest({
    id: 'test-1',
    method: 'test.rpc1',
    params: { key: 'value' },
  }),
  response: jsonrpcSuccessRespone({
    id: 'test-1',
    result: { key: 'value' },
  }),
}

const testRpcNotification = jsonrpcNotification({
  method: 'test.notification',
  params: { message: 'hello' },
})

const testRpcError = {
  request: jsonrpcRequest({
    id: 'test-2',
    method: 'test.rpc2',
    params: { key: 'value' },
  }),
  response: jsonrpcErrorResponse({
    id: 'test-2',
    error: {
      code: -32601,
      message: 'Method not found',
    },
  }),
}

let wsClient: WebSocketHandlerConnection['client']
// 创建 MSW 服务器
const server = setupServer(
  wsLink.addEventListener('connection', ({ client }) => {
    wsClient = client
    client.addEventListener('message', (event: MessageEvent) => {
      const message = parseJsonrpcMessage(event.data)

      if (isJsonrpcNotification(message)) {
        return
      }
      if (isJsonrpcRequest(message)) {
        if (message.id === 'test-1') {
          client.send(JSON.stringify(testRpcSuccess.response))
        } else if (message.id === 'test-2') {
          client.send(JSON.stringify(testRpcError.response))
        }
      }
    })
  })
)

describe('WebshellConnector', () => {
  let timer: number
  // 启动和停止 MSW 服务器
  beforeAll(() => {
    server.listen()
    timer = setInterval(() => wsClient.send(JSON.stringify(testRpcNotification)), 1000)
  })

  afterAll(() => {
    clearInterval(timer)
    server.close()
  })

  describe('WebshellConnector', () => {
    let connector: WebshellConnector

    beforeEach(() => {
      // 每个测试前创建新的连接器实例
      connector = new WebshellConnector(testUrl)
    })

    afterEach(() => {
      // 每个测试后关闭连接
      if (connector.readyState === WebSocket.OPEN) {
        connector.close()
      }
    })

    describe('构造函数和初始化', () => {
      it('应该正确创建 WebshellConnector 实例', () => {
        expect(connector).toBeInstanceOf(WebshellConnector)
        expect(connector).toBeInstanceOf(WebSocket)
        expect(connector.url).toBe(testUrl)
      })

      it('应该使用默认选项', () => {
        const defaultConnector = new WebshellConnector(testUrl)
        expect(defaultConnector).toBeInstanceOf(WebshellConnector)
      })

      it('应该接受自定义选项', () => {
        const customPromiseStore = new ExpiryMap(5000)
        const customEventEmitter = new EventEmitter()
        const customConnector = new WebshellConnector(testUrl, undefined, {
          promiseStore: customPromiseStore,
          eventEmitter: customEventEmitter,
          requestTimeout: 5000,
        })
        expect(customConnector).toBeInstanceOf(WebshellConnector)
      })
    })

})
